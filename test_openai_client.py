import base64
import os
from openai import OpenAI


def read_image_as_base64(image_path: str) -> str:
    """
    从本地文件读取图片并转换为base64格式

    Args:
        image_path: 图片的本地路径

    Returns:
        str: base64编码的图片数据，失败时返回空字符串
    """
    try:
        with open(image_path, "rb") as f:
            image_data = f.read()
            base64_data = base64.b64encode(image_data).decode('utf-8')
            return base64_data
    except Exception as e:
        print(f"图片处理异常: {e}")
        return ""


img1_base64 = read_image_as_base64("/Users/<USER>/Downloads/PFT_c31dc261.png")
# img2_base64 = read_image_as_base64("/Users/<USER>/Downloads/UROLOGY_UDS_0f41ba5f.png")

client = OpenAI(base_url="http://172.16.3.112:8000/v1", api_key="")
prompt = (
    'Please extract all the text from the image with the following requirements:\n'
    '1. Return tables in HTML format.\n'
    '2. Return all other text in Markdown format.'
)
messages = [
    {
        "role": "user",
        "content": [
            {
                "type": "text",
                "text": prompt
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/png;base64,{img1_base64}"
                }
            },
        ]
    }
]

# messages = self.get_few_shot(prompt) + messages
completion = client.chat.completions.create(
    model="MiniCPM-V-4_5",
    messages=messages,
    top_p=0.8,
    temperature=0,
    frequency_penalty=0,
    extra_body={
        "stop_token_ids": [1, 73440],
        "chat_template_kwargs": {"enable_thinking": False},
    }
)

print(completion.choices[0].message.content)
