#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Label Studio 回归测试脚本
用于自动化测试OCR结果与标注数据的一致性
"""

import base64
import json
import os
import sys
from typing import Dict

import requests
from openai import OpenAI

from dots_ocr.utils import dict_promptmode_to_prompt
from dots_ocr.utils.image_utils import fetch_image, get_image_by_fitz_doc, PILimage_to_base64


class LabelStudioTester:
    def __init__(self, json_file_path: str, ocr_endpoint: str = "http://localhost:9000/ocr/prediction"):
        """
        初始化测试器
        
        Args:
            json_file_path: Label Studio导出的JSON文件路径
            ocr_endpoint: OCR服务的API端点
        """
        self.json_file_path = json_file_path
        self.ocr_endpoint = ocr_endpoint
        self.tasks_data = None
        self.continue_on_wrong = True  # 控制是否在错误时继续

        self.llm_client = OpenAI(base_url="http://************:8000/v1", api_key="")
        self.llm_model = "MiniCPM-V-4_5"
        # self.llm_model = "POINTS-Reader"

        # 字符对等价名单 - 这些字符对被认为是相等的
        self.char_equals_pairs = {
            # 标点符号
            (',', '，'),  # 英文逗号 vs 中文逗号
            ('.', '。'),  # 英文句号 vs 中文句号
            ('?', '？'),  # 英文问号 vs 中文问号
            ('!', '！'),  # 英文感叹号 vs 中文感叹号
            (':', '：'),  # 英文冒号 vs 中文冒号
            (';', '；'),  # 英文分号 vs 中文分号
            ('(', '（'),  # 英文左括号 vs 中文左括号
            (')', '）'),  # 英文右括号 vs 中文右括号
            ('[', '［'),  # 英文左方括号 vs 中文左方括号
            (']', '］'),  # 英文右方括号 vs 中文右方括号
            ('{', '｛'),  # 英文左花括号 vs 中文左花括号
            ('}', '｝'),  # 英文右花括号 vs 中文右花括号
            ('\u0022', '\u201C'),  # 英文双引号 vs 中文左双引号 (U+0022 vs U+201C)
            ('\u0022', '\u201D'),  # 英文双引号 vs 中文右双引号 (U+0022 vs U+201D)
            ('-', '－'),  # 英文连字符 vs 中文连字符
            ('_', '＿'),  # 英文下划线 vs 中文下划线
            # 数字
            ('0', '０'), ('1', '１'), ('2', '２'), ('3', '３'), ('4', '４'),
            ('5', '５'), ('6', '６'), ('7', '７'), ('8', '８'), ('9', '９'),
        }

        # 创建双向映射字典，支持一对多映射，方便查找
        self.char_equals_map = {}
        for char1, char2 in self.char_equals_pairs:
            # 为每个字符创建等价字符集合
            if char1 not in self.char_equals_map:
                self.char_equals_map[char1] = set()
            if char2 not in self.char_equals_map:
                self.char_equals_map[char2] = set()

            # 添加双向映射
            self.char_equals_map[char1].add(char2)
            self.char_equals_map[char2].add(char1)

    def load_annotation_data(self) -> bool:
        """
        读取Label Studio导出的标注数据JSON文件
        
        Returns:
            bool: 读取是否成功
        """
        try:
            print(f"正在读取标注数据文件: {self.json_file_path}")
            with open(self.json_file_path, "r", encoding="utf-8") as f:
                self.tasks_data = json.load(f)
            print(f"成功读取 {len(self.tasks_data)} 个标注任务")
            return True
        except FileNotFoundError:
            print(f"错误: 找不到文件 {self.json_file_path}")
            return False
        except json.JSONDecodeError as e:
            print(f"错误: JSON文件格式不正确 - {e}")
            return False
        except Exception as e:
            print(f"错误: 读取文件时发生异常 - {e}")
            return False

    def download_image_as_base64(self, image_url: str) -> str:
        """
        从URL下载图片并转换为base64格式
        
        Args:
            image_url: 图片的URL
            
        Returns:
            str: base64编码的图片数据，失败时返回空字符串
        """
        try:
            response = requests.get(image_url, timeout=30)
            response.raise_for_status()

            # 将图片数据转换为base64
            image_data = response.content
            base64_data = base64.b64encode(image_data).decode('utf-8')
            return base64_data

        except requests.exceptions.RequestException as e:
            print(f"图片下载失败: {e}")
            return ""
        except Exception as e:
            print(f"图片处理异常: {e}")
            return ""

    def read_image_as_base64(self, image_path: str) -> str:
        """
        从本地文件读取图片并转换为base64格式

        Args:
            image_path: 图片的本地路径

        Returns:
            str: base64编码的图片数据，失败时返回空字符串
        """
        try:
            with open(image_path, "rb") as f:
                image_data = f.read()
                base64_data = base64.b64encode(image_data).decode('utf-8')
                return base64_data
        except Exception as e:
            print(f"图片处理异常: {e}")
            return ""

    def get_few_shot(self, prompt: str):
        img1_base64 = self.read_image_as_base64("/Users/<USER>/Downloads/UROLOGY_UDS_f21eeb19.png")
        return [{
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{img1_base64}"
                    }
                },
                {
                    "type": "text",
                    "text": f"<|img|><|imgpad|><|endofimg|>{prompt}"
                }
            ]
        },
            {
                "role": "assistant",
                "content": [
                    {
                        "type": "text",
                        "text": "1、睡眠分期：入睡潜伏期11.5分钟；入睡后觉醒次数为29次，睡眠连续性较差；总睡眠时间为387.5分钟；睡眠效率为79.3"
                                "%，整晚睡眠质量欠佳；其睡眠结构：稳定睡眠占比15.5%，不稳定睡眠占比62.7%，REM期睡眠占比21.8%。2、呼吸及相关事件：在整夜睡眠中，40.0"
                                "%以上时间处于仰卧位；呼吸暂停与低通气指数（AHI）达10.7次每小时；打鼾时间占睡眠时间为18.7%。综上，符合“阻塞性睡眠呼吸暂停低通气综合征("
                                "轻度)”诊断标准。3、心电图：清醒期最高心率为108.0次/分钟，睡眠期最高心率为107.0次/分钟。4、血压：醒后血压：148/91mm/Hg。5"
                                "、血氧饱和度：整晚最低血氧值为86%、平均血氧值为94%。"
                    }
                ]
            }]

    def get_ocr_result_from_llm(self, base64_image: str, prompt: str) -> str:
        prompt = (
            'Please extract all the text from the image with the following requirements:\n'
            '1. Return tables in HTML format.\n'
            '2. Return all other text in Markdown format.'
        )
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": base64_image
                        }
                    },
                ]
            }
        ]

        # messages = self.get_few_shot(prompt) + messages
        completion = self.llm_client.chat.completions.create(
            model=self.llm_model,
            messages=messages,
            top_p=0.8,
            temperature=0,
            frequency_penalty=0,
            extra_body={
                "stop_token_ids": [1, 73440],
                "chat_template_kwargs": {"enable_thinking": False},
            }
        )
        return completion.choices[0].message.content

    def get_ocr_result(self, base64_image: str) -> str:
        """
        调用OCR服务获取文本识别结果
        
        Args:
            base64_image: base64编码的图片数据
            
        Returns:
            str: OCR识别的文本结果，失败时返回空字符串
        """
        try:
            request_data = {
                "dataType": "image",
                "matchArea": {
                    "page": 0,
                    "startX": 0,
                    "startY": 0,
                    "cutHeight": -1,
                    "cutWidth": -1,
                    "recModelId": "ppocrv4_doc_server"
                },
                "images": [base64_image]
            }

            response = requests.post(
                self.ocr_endpoint,
                json=request_data,
                timeout=60,
                headers={'Content-Type': 'application/json'}
            )
            response.raise_for_status()

            result = response.json()
            if "ocrResults" in result and len(result["ocrResults"]) > 0:
                ocr_text = result["ocrResults"][0].get("text_result", "")
                return ocr_text
            else:
                print("OCR服务返回空结果")
                return ""

        except requests.exceptions.RequestException as e:
            print(f"OCR请求失败: {e}")
            return ""
        except json.JSONDecodeError as e:
            print(f"OCR返回JSON格式错误: {e}")
            return ""
        except Exception as e:
            print(f"OCR服务异常: {e}")
            return ""

    def show_string_differences(self, ocr_text: str, target_text: str) -> None:
        """
        显示两个字符串的详细差异

        Args:
            ocr_text: OCR识别的文本
            target_text: 目标文本
        """
        print("\n" + "=" * 60)
        print("字符串差异分析")
        print("=" * 60)

        # 显示原始文本（截断显示）
        max_display_length = 150
        print(f"OCR文本 (长度:{len(ocr_text)}):")
        print(f"   '{ocr_text[:max_display_length]}{'...' if len(ocr_text) > max_display_length else ''}'")
        print(f"目标文本 (长度:{len(target_text)}):")
        print(f"   '{target_text[:max_display_length]}{'...' if len(target_text) > max_display_length else ''}'")
        print()

        # 显示处理后的文本（移除换行符和空格后用于比较的文本）
        processed_ocr = ocr_text.strip().replace('\n', '').replace('\r', '').replace(' ', '')
        processed_target = target_text.strip().replace('\n', '').replace('\r', '').replace(' ', '')

        if processed_ocr != ocr_text.strip() or processed_target != target_text.strip():
            print("处理后的文本（移除换行符和空格）:")
            print(f"OCR文本 (长度:{len(processed_ocr)}):")
            print(
                f"   '{processed_ocr[:max_display_length]}{'...' if len(processed_ocr) > max_display_length else ''}'")
            print(f"目标文本 (长度:{len(processed_target)}):")
            print(
                f"   '{processed_target[:max_display_length]}{'...' if len(processed_target) > max_display_length else ''}'")
            print()

        # 字符级差异分析（使用处理后的文本）
        self._show_character_differences(processed_ocr, processed_target)

        print("=" * 60)

    def _show_character_differences(self, ocr_text: str, target_text: str) -> None:
        """
        显示字符级别的差异
        
        Args:
            ocr_text: OCR识别的文本
            target_text: 目标文本
        """
        print("字符级差异:")

        # 找出第一个不同的字符位置
        min_length = min(len(ocr_text), len(target_text))
        first_diff_pos = -1

        for i in range(min_length):
            if not self._chars_are_equal(ocr_text[i], target_text[i]):
                first_diff_pos = i
                break

        if first_diff_pos >= 0:
            print(f"   首个差异位置: {first_diff_pos}")

            # 显示差异位置周围的文本
            context_range = 15
            start_pos = max(0, first_diff_pos - context_range)
            end_pos = min(len(ocr_text), first_diff_pos + context_range + 1)

            ocr_context = ocr_text[start_pos:end_pos]
            print(f"   OCR: '{ocr_context}'")

            # 标记差异位置
            marker_pos = first_diff_pos - start_pos
            # 计算实际显示位置，考虑中文字符的显示宽度
            display_width = 9  # "   OCR: '" 的显示宽度
            for i, char in enumerate(ocr_context):
                if i >= marker_pos:
                    break
                # 判断是否为宽字符（中文、日文、韩文等）
                if self._is_wide_char(char):
                    display_width += 2
                else:
                    display_width += 1

            marker = ' ' * display_width + '^'
            print(f"{marker}")

            end_pos_target = min(len(target_text), first_diff_pos + context_range + 1)
            target_context = target_text[start_pos:end_pos_target]
            print(f"   目标: '{target_context}'")

            # 显示具体的字符差异
            if first_diff_pos < len(ocr_text) and first_diff_pos < len(target_text):
                ocr_char = ocr_text[first_diff_pos]
                target_char = target_text[first_diff_pos]
                print(f"   差异: OCR='{ocr_char}' vs 目标='{target_char}'")

        # 统计差异信息
        diff_count = 0
        for i in range(min_length):
            if not self._chars_are_equal(ocr_text[i], target_text[i]):
                diff_count += 1
        diff_count += abs(len(ocr_text) - len(target_text))  # 加上长度差异

        if len(ocr_text) != len(target_text):
            print(f"   长度差异: {abs(len(ocr_text) - len(target_text))} 个字符")
        print(f"   总计差异: {diff_count} 个字符")

    def _is_wide_char(self, char: str) -> bool:
        """
        判断字符是否为宽字符（中文、日文、韩文等）
        
        Args:
            char: 要判断的字符
            
        Returns:
            bool: True表示是宽字符
        """
        # 获取字符的Unicode分类

        # 东亚宽字符的Unicode范围
        code = ord(char)

        # 中文字符范围
        if 0x4e00 <= code <= 0x9fff:  # CJK统一汉字
            return True
        if 0x3400 <= code <= 0x4dbf:  # CJK扩展A
            return True
        if 0x20000 <= code <= 0x2a6df:  # CJK扩展B
            return True
        if 0x2a700 <= code <= 0x2b73f:  # CJK扩展C
            return True
        if 0x2b740 <= code <= 0x2b81f:  # CJK扩展D
            return True

        # 日文字符
        if 0x3040 <= code <= 0x309f:  # 平假名
            return True
        if 0x30a0 <= code <= 0x30ff:  # 片假名
            return True

        # 韩文字符
        if 0xac00 <= code <= 0xd7af:  # 韩文音节
            return True
        if 0x1100 <= code <= 0x11ff:  # 韩文字母
            return True

        # 全角字符
        if 0xff00 <= code <= 0xffef:  # 全角ASCII、全角标点
            return True

        # 其他常见的宽字符
        if 0x2e80 <= code <= 0x2eff:  # CJK部首补充
            return True
        if 0x2f00 <= code <= 0x2fdf:  # 康熙部首
            return True
        if 0x3000 <= code <= 0x303f:  # CJK符号和标点
            return True

        return False

    def _chars_are_equal(self, char1: str, char2: str) -> bool:
        """
        检查两个字符是否相等（包括等价字符对）
        
        Args:
            char1: 第一个字符
            char2: 第二个字符
            
        Returns:
            bool: True表示字符相等或等价
        """
        if char1 == char2:
            return True

        # 检查是否在等价字符对中
        equal_chars = self.char_equals_map.get(char1, set())
        return char2 in equal_chars

    def _strings_are_equal(self, str1: str, str2: str) -> bool:
        """
        使用等价字符对规则比较两个字符串是否相等
        在比较前会移除换行符和空格，使得包含这些字符的文本和不包含的文本被认为是相等的

        Args:
            str1: 第一个字符串
            str2: 第二个字符串

        Returns:
            bool: True表示字符串相等或等价
        """
        # 先去除首尾空白字符，然后移除所有换行符和空格
        str1 = str1.strip().replace('\n', '').replace('\r', '').replace(' ', '')
        str2 = str2.strip().replace('\n', '').replace('\r', '').replace(' ', '')

        if len(str1) != len(str2):
            return False

        for i in range(len(str1)):
            if not self._chars_are_equal(str1[i], str2[i]):
                return False

        return True

    def ask_user_continue(self, task_index: int, total_tasks: int, ocr_text: str = None,
                          target_text: str = None) -> bool:
        """
        询问用户是否继续测试
        
        Args:
            task_index: 当前任务索引
            total_tasks: 总任务数
            ocr_text: OCR识别的文本（用于重新显示差异）
            target_text: 目标文本（用于重新显示差异）
            
        Returns:
            bool: True表示继续，False表示退出
        """
        print(f"\n当前进度: {task_index}/{total_tasks}")
        print("发现OCR结果与目标文本不匹配！")

        while True:
            try:
                choice = input(
                    "\n请选择操作:\n1. 继续下一个任务 (c/continue)\n2. 退出测试 (q/quit)\n3. 重新查看详细差异 (d/diff)\n请输入选择: ").strip().lower()

                if choice in ['c', 'continue', '1', '继续']:
                    return True
                elif choice in ['q', 'quit', '2', '退出']:
                    print("用户选择退出测试")
                    return False
                elif choice in ['d', 'diff', '3', '差异']:
                    if ocr_text is not None and target_text is not None:
                        self.show_string_differences(ocr_text, target_text)
                    else:
                        print("无法显示差异：缺少文本数据")
                    continue
                else:
                    print("无效选择，请重新输入")

            except KeyboardInterrupt:
                print("\n\n用户中断测试")
                return False
            except EOFError:
                print("\n\n输入结束，退出测试")
                return False

    def update_annotation_result(self, task: Dict, ocr_text: str, target_text: str) -> bool:
        """
        更新标注结果中的OCR文本和正确性判断
        
        Args:
            task: 标注任务数据
            ocr_text: OCR识别的文本
            target_text: 目标文本
            
        Returns:
            bool: 更新是否成功
        """
        try:
            if not task.get("annotations"):
                print("警告: 任务中没有标注数据")
                return False

            # 判断OCR结果是否正确（使用等价字符对规则）
            is_correct = self._strings_are_equal(ocr_text, target_text)
            correct_value = "Correct" if is_correct else "Wrong"

            updated = False
            for annotation in task["annotations"]:
                if not annotation.get("result"):
                    continue

                for result_item in annotation["result"]:
                    # 更新OCR文本
                    if result_item.get("from_name") == "ocr_text":
                        result_item["value"]["text"] = [ocr_text]
                        updated = True

                    # 更新正确性判断
                    elif result_item.get("from_name") == "isCorrect":
                        result_item["value"]["choices"] = [correct_value]
                        updated = True

            return updated

        except Exception as e:
            print(f"错误: 更新标注结果时发生异常 - {e}")
            return False

    def save_updated_data(self, output_path: str = None) -> bool:
        """
        将更新后的标注数据保存到JSON文件
        
        Args:
            output_path: 输出文件路径，如果为None则覆盖原文件
            
        Returns:
            bool: 保存是否成功
        """
        try:
            save_path = output_path or self.json_file_path
            print(f"正在保存更新后的标注数据到: {save_path}")

            with open(save_path, "w", encoding="utf-8") as f:
                json.dump(self.tasks_data, f, ensure_ascii=False, indent=2)

            print("标注数据保存成功")
            return True

        except Exception as e:
            print(f"错误: 保存文件时发生异常 - {e}")
            return False

    def run_regression_test(self) -> Dict[str, int]:
        """
        运行完整的回归测试流程
        
        Returns:
            Dict[str, int]: 测试结果统计
        """
        print("开始Label Studio回归测试")

        # 初始化统计数据
        stats = {
            "total": 0,
            "processed": 0,
            "correct": 0,
            "wrong": 0,
            "errors": 0
        }

        # 1. 读取标注数据
        if not self.load_annotation_data():
            return stats

        # 2. 遍历标注数据
        for i, task in enumerate(self.tasks_data, 1):
            print(f"\n处理任务 {i}/{len(self.tasks_data)}")
            stats["total"] += 1

            try:
                # 获取任务ID
                task_id = task.get("id", f"task_{i}")

                # 获取图片URL
                image_url = task.get("data", {}).get("url", "")
                if not image_url:
                    print("警告: 任务中没有图片URL")
                    stats["errors"] += 1
                    continue

                # 获取目标文本
                target_text = ""
                if task.get("annotations"):
                    for annotation in task["annotations"]:
                        for result_item in annotation.get("result", []):
                            if result_item.get("from_name") == "target_text":
                                target_text = result_item.get("value", {}).get("text", [""])[0]
                                break

                if not target_text:
                    print("任务中没有目标文本")
                    stats["errors"] += 1
                    continue

                # 3. 下载图片并转换为base64
                base64_image = self.download_image_as_base64(image_url)
                if not base64_image:
                    stats["errors"] += 1
                    continue

                # ocr_request = OCRRequest(images=[],
                #                          dataType="image",
                #                          matchArea=MatchArea(page=0,
                #                                              startX=0,
                #                                              startY=0,
                #                                              cutHeight=-1,
                #                                              cutWidth=-1,
                #                                              recModelId=None))
                #
                # image = ocr_server.get_img_from_area(base64.b64decode(base64_image), ocr_request)
                # _, buffer = cv2.imencode('.png', image)
                # base64_image = base64.b64encode(buffer).decode('utf-8')

                origin_image = fetch_image(image=image_url)
                origin_image = get_image_by_fitz_doc(origin_image, target_dpi=300)
                base64_image = PILimage_to_base64(origin_image)
                # 4. 获取OCR结果
                prompt = dict_promptmode_to_prompt["prompt_ocr"]
                ocr_text = self.get_ocr_result_from_llm(base64_image, prompt)
                if not ocr_text:
                    stats["errors"] += 1
                    continue

                # 5. 更新标注结果
                if self.update_annotation_result(task, ocr_text, target_text):
                    stats["processed"] += 1

                    # 统计正确性（使用等价字符对规则）
                    is_correct = self._strings_are_equal(ocr_text, target_text)
                    if is_correct:
                        stats["correct"] += 1
                        print("OCR结果正确")
                    else:
                        stats["wrong"] += 1
                        print("OCR结果不正确")
                        print(f"任务ID: {task_id}")
                        print(f"图片URL: {image_url}")

                        # 显示字符串差异
                        self.show_string_differences(ocr_text, target_text)

                        # 询问用户是否继续
                        if not self.ask_user_continue(i, len(self.tasks_data), ocr_text, target_text):
                            print("测试被用户中断")
                            break
                else:
                    stats["errors"] += 1

            except Exception as e:
                print(f"错误: 处理任务时发生异常 - {e}")
                stats["errors"] += 1

        # 6. 保存更新后的数据
        if stats["processed"] > 0:
            self.save_updated_data()

        # 输出测试结果
        self.print_test_results(stats)

        return stats

    def print_test_results(self, stats: Dict[str, int]):
        """
        打印测试结果统计
        
        Args:
            stats: 测试结果统计数据
        """
        print("\n测试结果统计")
        print("=" * 40)
        print(f"总任务数: {stats['total']}")
        print(f"成功处理: {stats['processed']}")
        print(f"处理错误: {stats['errors']}")

        if stats['processed'] > 0:
            print(f"OCR正确: {stats['correct']} ({stats['correct'] / stats['processed']:.1%})")
            print(f"OCR错误: {stats['wrong']} ({stats['wrong'] / stats['processed']:.1%})")

        print("=" * 40)


def main():
    """主函数"""
    # 使用默认的测试文件路径
    json_file_path = "/Users/<USER>/Downloads/project-4-at-2025-09-01-07-38-d49fca5d.json"

    # 检查文件是否存在
    if not os.path.exists(json_file_path):
        print(f"错误: 测试文件不存在 - {json_file_path}")
        print("请确保文件路径正确，或者修改脚本中的文件路径")
        sys.exit(1)

    # 创建测试器并运行测试
    tester = LabelStudioTester(json_file_path)
    results = tester.run_regression_test()

    # 根据测试结果设置退出码
    if results["errors"] > 0:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()
